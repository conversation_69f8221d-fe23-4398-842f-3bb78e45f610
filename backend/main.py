"""CodeOCR API server for converting code screenshots to editable code."""

import json
import instructor
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from instructor.multimodal import Image
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, field_validator


class CodeOCRRequest(BaseModel):
    image_source: str = Field(..., description="Base64 encoded image data or image URL")
    api_key: str = Field(..., description="API key for OpenAI")
    api_base: str = Field("https://api.openai.com/v1", description="API base URL")
    model_name: str = Field(..., description="Model name to use")


class CodeOCRResponse(BaseModel):
    """Response model for code extraction results.

    This model structures the output from the code extraction service,
    providing both the extracted code content and the automatically
    detected programming language for proper syntax highlighting.

    Attributes:
        code: The extracted code content from the image, preserving
              original formatting and structure as much as possible.
        language: The detected programming language (e.g., 'python', 'javascript').
                  Automatically normalized to lowercase for consistency.
    """

    code: str = Field(
        ...,
        description="Extracted code content from the image with preserved formatting.",
    )
    language: str = Field(
        ...,
        description="Detected programming language (normalized to lowercase).",
    )

    @field_validator("language")
    @classmethod
    def lowercase_language(cls, v: str) -> str:
        return v.lower()


class CodeOCRResponseChunk(BaseModel):
    """Partial response model for streaming code extraction results."""

    code_chunk: str | None = None
    language_chunk: str | None = None


# Initialize FastAPI application with metadata
app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

# Configure CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(request: CodeOCRRequest) -> CodeOCRResponse:
    """Extract code from an image using a vision model."""
    # Create OpenAI client with provided configuration
    openai_client = AsyncOpenAI(
        api_key=request.api_key,
        base_url=request.api_base,
    )

    # Create instructor client for structured output generation
    instructor_client = instructor.from_openai(
        openai_client,
        mode=instructor.Mode.JSON,
    )

    response = await instructor_client.chat.completions.create(
        model=request.model_name,
        response_model=CodeOCRResponse,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image. Keep the original code formatting.",
                    Image.autodetect(request.image_source),
                ],
            },
        ],
    )
    return response


async def stream_codeocr(request: CodeOCRRequest):
    """Stream code extraction from an image, following instructor library best practices."""
    # Create OpenAI client
    openai_client = AsyncOpenAI(api_key=request.api_key, base_url=request.api_base)

    # Create instructor client for structured, streaming output
    instructor_client = instructor.from_openai(
        openai_client, mode=instructor.Mode.JSON
    )

    # Use instructor to stream structured output
    response_stream = await instructor_client.chat.completions.create(
        model=request.model_name,
        response_model=CodeOCRResponse,
        stream=True,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image. Keep the original code formatting.",
                    Image.autodetect(request.image_source),
                ],
            },
        ],
    )

    async for partial_response in response_stream:
        yield partial_response.model_dump_json() + "\n"


@app.post("/api/v1/codeocr/stream")
async def codeocr_stream(request: CodeOCRRequest):
    """API endpoint for streaming code extraction."""
    return StreamingResponse(
        stream_codeocr(request), media_type="application/x-ndjson"
    )


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
