import { NextRequest, NextResponse } from "next/server";

// Helper to create a streaming response
function createStreamingResponse(backendResponse: Response): Response {
  const stream = new ReadableStream({
    async start(controller) {
      if (!backendResponse.body) {
        controller.close();
        return;
      }
      const reader = backendResponse.body.getReader();
      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          const chunk = decoder.decode(value, { stream: true });
          controller.enqueue(new TextEncoder().encode(chunk));
        }
      } catch (error) {
        console.error("Error while reading from backend stream:", error);
        controller.error(error);
      } finally {
        controller.close();
      }
    },
  });

  return new Response(stream, {
    headers: { "Content-Type": "application/x-ndjson" },
  });
}

export async function POST(request: NextRequest) {
  try {
    // Get the form data from the request
    const formData = await request.formData();

    // Check if formData contains a file
    const imageFile = formData.get("file");
    if (!imageFile || !(imageFile instanceof File)) {
      return NextResponse.json(
        { error: "No image file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(imageFile.type)) {
      return NextResponse.json(
        {
          error:
            "Invalid file type. Please upload PNG, JPG, JPEG, or WebP images.",
        },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSize) {
      return NextResponse.json(
        { error: "File size exceeds 5MB limit." },
        { status: 400 }
      );
    }

    // Convert file to base64 for the FastAPI backend
    const arrayBuffer = await imageFile.arrayBuffer();
    const base64 = Buffer.from(arrayBuffer).toString("base64");
    const mimeType = imageFile.type;
    const base64DataUrl = `data:${mimeType};base64,${base64}`;

    // Get required API settings from form data
    const apiKey = formData.get("api_key") as string | null;
    const apiBase = formData.get("api_base") as string | null;
    const modelName = formData.get("model_name") as string | null;

    // Validate required API settings
    if (!apiKey) {
      return NextResponse.json(
        { error: "API key is required. Please configure your API settings." },
        { status: 400 }
      );
    }

    if (!modelName) {
      return NextResponse.json(
        {
          error: "Model name is required. Please configure your API settings.",
        },
        { status: 400 }
      );
    }

    // Prepare the JSON payload for the FastAPI backend
    const payload = {
      image_source: base64DataUrl,
      api_key: apiKey,
      api_base: apiBase || "https://api.openai.com/v1",
      model_name: modelName,
    };

    // Forward the request to the FastAPI backend's streaming endpoint
    const backendUrl =
      process.env.BACKEND_URL || "http://127.0.0.1:8000";
    const backendStreamUrl = `${backendUrl}/api/v1/codeocr/stream`;

    const backendResponse = await fetch(backendStreamUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/x-ndjson",
      },
      body: JSON.stringify(payload),
    });

    if (!backendResponse.ok) {
      const errorData = await backendResponse.json().catch(() => ({
        detail: "Backend processing failed with non-JSON response.",
      }));
      return NextResponse.json(
        { error: errorData.detail || "Backend processing failed" },
        { status: backendResponse.status }
      );
    }

    // Return a streaming response to the client
    return createStreamingResponse(backendResponse);
  } catch (error) {
    console.error("Error in CodeOCR API route:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
