"use client";

import APISettingsDialog from "@/components/APISettingsDialog";
import ImageUpload from "@/components/ImageUpload";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { BundledLanguage } from "@/components/ui/kibo-ui/code-block";
import {
  CodeBlock,
  CodeBlockBody,
  CodeBlockContent,
  CodeBlockCopyButton,
  CodeBlockFilename,
  CodeBlockFiles,
  CodeBlockHeader,
  CodeBlockItem,
} from "@/components/ui/kibo-ui/code-block";
import { Loader2, ScanText, Code } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface CodeOCRResponse {
  language: string;
  code: string;
}

interface APISettings {
  apiKey: string;
  apiBase: string;
  modelName: string;
}

export default function CodeOCRApp() {
  const t = useTranslations("CodeOCR");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<CodeOCRResponse | null>(null);
  const [apiSettings, setApiSettings] = useState<APISettings>({
    apiKey: "",
    apiBase: "",
    modelName: "",
  });

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setResult(null);
  }, []);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setResult(null);
  }, []);

  const processImage = async () => {
    if (!selectedFile) {
      toast.error(t("selectImageFirst"));
      return;
    }
    if (!apiSettings.apiKey) {
      toast.error(t("apiKeyRequired"));
      return;
    }
    if (!apiSettings.modelName) {
      toast.error(t("modelNameRequired"));
      return;
    }

    setIsProcessing(true);
    setResult({ language: "plaintext", code: "" }); // Reset result for streaming

    try {
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("api_key", apiSettings.apiKey);
      formData.append("api_base", apiSettings.apiBase || "https://api.openai.com/v1");
      formData.append("model_name", apiSettings.modelName);

      const response = await fetch("/api/v1/codeocr", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.detail || t("extractFailed"));
      }

      if (!response.body) {
        throw new Error("Response body is empty.");
      }

      // Handle streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // Keep the last partial line in buffer

        for (const line of lines) {
          if (line.trim() === "") continue;
          try {
            const partialResult = JSON.parse(line) as CodeOCRResponse;
            setResult(partialResult);
          } catch (e) {
            console.error("Failed to parse stream chunk:", line, e);
          }
        }
      }
      toast.success(t("extractSuccess"));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : t("extractFailed");
      toast.error(errorMessage);
      setResult(null); // Clear result on error
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="w-full space-y-4">
      <Card className="shadow-md">
        <CardContent className="p-4 sm:p-6">
          <div className="space-y-4 sm:space-y-6">
            <ImageUpload
              onFileSelect={handleFileSelect}
              onRemoveFile={handleRemoveFile}
              selectedFile={selectedFile}
              disabled={isProcessing}
            />

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="w-full sm:w-auto">
                <APISettingsDialog onSettingsChange={setApiSettings} />
              </div>

              <Button
                onClick={processImage}
                disabled={!selectedFile || isProcessing}
                className="w-full sm:flex-1"
                size="default"
                variant="default"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("processing")}
                  </>
                ) : (
                  <>
                    <ScanText className="mr-2 h-4 w-4" />
                    {t("extractCode")}
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {result && (
        <CodeBlock
          data={[
            {
              language: result.language,
              filename: "",
              code: result.code,
            },
          ]}
          defaultValue={result.language}
        >
          <CodeBlockHeader>
            <CodeBlockFiles>
              {(item) => (
                <CodeBlockFilename key={item.language} value={item.language}>
                  <div className="flex items-center gap-2">
                    <Code /> {t("extractedCode")}
                  </div>
                </CodeBlockFilename>
              )}
            </CodeBlockFiles>
            <CodeBlockCopyButton />
          </CodeBlockHeader>
          <CodeBlockBody>
            {(item) => (
              <CodeBlockItem key={item.language} value={item.language}>
                <CodeBlockContent language={item.language as BundledLanguage}>
                  {item.code}
                </CodeBlockContent>
              </CodeBlockItem>
            )}
          </CodeBlockBody>
        </CodeBlock>
      )}
    </div>
  );
}
