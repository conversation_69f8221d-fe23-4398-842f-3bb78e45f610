"""CodeOCR API server for converting code screenshots to editable code."""

import instructor
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from instructor.multimodal import Image
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, field_validator


class CodeOCRRequest(BaseModel):
    image_source: str = Field(..., description="Base64 encoded image data or image URL")
    api_key: str = Field(..., description="API key for OpenAI")
    api_base: str = Field("https://api.openai.com/v1", description="API base URL")
    model_name: str = Field(..., description="Model name to use")


class CodeOCRResponse(BaseModel):
    """Response model for code extraction results.

    This model structures the output from the code extraction service,
    providing both the extracted code content and the automatically
    detected programming language for proper syntax highlighting.

    Attributes:
        code: The extracted code content from the image, preserving
              original formatting and structure as much as possible.
        language: The detected programming language (e.g., 'python', 'javascript').
                  Automatically normalized to lowercase for consistency.
    """

    code: str = Field(
        ...,
        description="Extracted code content from the image with preserved formatting.",
    )
    language: str = Field(
        ...,
        description="Detected programming language (normalized to lowercase).",
    )

    @field_validator("language")
    @classmethod
    def lowercase_language(cls, v: str) -> str:
        return v.lower()


class CodeOCRResponseChunk(BaseModel):
    """Partial response model for streaming code extraction results."""

    code_chunk: str | None = None
    language_chunk: str | None = None


# Initialize FastAPI application with metadata
app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

# Configure CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(request: CodeOCRRequest) -> CodeOCRResponse:
    """Extract code from an image using a vision model."""
    # Create OpenAI client with provided configuration
    openai_client = AsyncOpenAI(
        api_key=request.api_key,
        base_url=request.api_base,
    )

    # Create instructor client for structured output generation
    instructor_client = instructor.from_openai(
        openai_client,
        mode=instructor.Mode.JSON,
    )

    response = await instructor_client.chat.completions.create(
        model=request.model_name,
        response_model=CodeOCRResponse,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image. Keep the original code formatting.",
                    Image.autodetect(request.image_source),
                ],
            },
        ],
    )
    return response


async def stream_codeocr(request: CodeOCRRequest):
    """流式响应实现 - 支持真实 API 和模拟数据"""
    import asyncio
    import json

    # 如果是测试模式，使用模拟数据
    if request.api_key == "test":
        print("使用测试模式，生成模拟数据")
        code_parts = [
            {"code": "", "language": "python"},
            {"code": "def", "language": "python"},
            {"code": "def hello", "language": "python"},
            {"code": "def hello_world", "language": "python"},
            {"code": "def hello_world():", "language": "python"},
            {"code": "def hello_world():\n    print", "language": "python"},
            {"code": 'def hello_world():\n    print("Hello', "language": "python"},
            {
                "code": 'def hello_world():\n    print("Hello, World!")',
                "language": "python",
            },
        ]

        for i, part in enumerate(code_parts):
            data = json.dumps(part) + "\n"
            print(f"发送第 {i + 1} 块数据：{data.strip()}")
            yield data
            await asyncio.sleep(0.5)
        print("测试模式数据发送完成")
        return

    # 真实 API 调用
    try:
        openai_client = AsyncOpenAI(api_key=request.api_key, base_url=request.api_base)

        response = await openai_client.chat.completions.create(
            model=request.model_name,
            stream=True,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Extract code from this image. Return only the code without any explanation. Keep the original formatting.",
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": request.image_source},
                        },
                    ],
                },
            ],
        )

        accumulated_code = ""
        detected_language = "text"

        async for chunk in response:
            if chunk.choices[0].delta.content:
                accumulated_code += chunk.choices[0].delta.content

                # 简单的语言检测
                if not detected_language or detected_language == "text":
                    if "def " in accumulated_code or "import " in accumulated_code:
                        detected_language = "python"
                    elif (
                        "function " in accumulated_code or "const " in accumulated_code
                    ):
                        detected_language = "javascript"
                    elif (
                        "public class" in accumulated_code
                        or "import java" in accumulated_code
                    ):
                        detected_language = "java"

                result = {"code": accumulated_code, "language": detected_language}
                yield json.dumps(result) + "\n"

    except Exception as e:
        print(f"API Error: {e}")
        error_result = {"code": f"Error: {str(e)}", "language": "text"}
        yield json.dumps(error_result) + "\n"


@app.post("/api/v1/codeocr/stream")
async def codeocr_stream(request: CodeOCRRequest):
    """API endpoint for streaming code extraction."""
    print(
        f"收到流式请求：api_key={request.api_key[:10]}..., model={request.model_name}"
    )
    return StreamingResponse(stream_codeocr(request), media_type="application/x-ndjson")


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
}
